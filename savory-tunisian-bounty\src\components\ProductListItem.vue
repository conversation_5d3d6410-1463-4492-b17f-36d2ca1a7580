<template>
  <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
    <div class="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-6">
      <!-- Product Image -->
      <div class="flex-shrink-0">
        <div class="relative">
          <img
            :src="product.image_url || '/placeholder-product.jpg'"
            :alt="product.name"
            class="w-full sm:w-32 h-32 object-cover rounded-lg"
            @error="handleImageError"
          />
          <div class="absolute top-2 right-2">
            <span
              v-if="product.stock_quantity <= 5"
              class="bg-red-500 text-white text-xs px-2 py-1 rounded-full"
            >
              Low Stock
            </span>
          </div>
        </div>
      </div>

      <!-- Product Info -->
      <div class="flex-1 min-w-0">
        <div class="flex flex-col sm:flex-row sm:items-start sm:justify-between">
          <div class="flex-1">
            <h3 class="text-xl font-semibold text-gray-900 mb-2">
              {{ product.name }}
            </h3>
            
            <p class="text-gray-600 mb-3 line-clamp-2">
              {{ product.description }}
            </p>

            <!-- Product Tags/Categories -->
            <div class="flex flex-wrap gap-2 mb-4">
              <span
                v-if="product.category"
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800"
              >
                {{ getCategoryName(product.category_id) }}
              </span>
              
              <span
                v-if="product.origin_region"
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
              >
                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
                </svg>
                {{ product.origin_region }}
              </span>

              <!-- Dietary badges -->
              <span
                v-if="product.is_organic"
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
              >
                Organic
              </span>
              
              <span
                v-if="product.is_vegan"
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
              >
                Vegan
              </span>
              
              <span
                v-if="product.is_gluten_free"
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800"
              >
                Gluten Free
              </span>
            </div>

            <!-- Stock Info -->
            <div class="flex items-center text-sm text-gray-500">
              <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
              </svg>
              {{ product.stock_quantity }} in stock
            </div>
          </div>

          <!-- Price and Actions -->
          <div class="flex flex-col items-end space-y-4 mt-4 sm:mt-0">
            <div class="text-right">
              <div class="text-3xl font-bold text-primary-600">
                ${{ product.price.toFixed(2) }}
              </div>
              <div v-if="isInCart" class="text-sm text-gray-500 mt-1">
                {{ cartQuantity }} in cart
              </div>
            </div>

            <div class="flex flex-col sm:flex-row gap-3">
              <button 
                @click="viewProduct" 
                class="btn-outline px-4 py-2 text-sm whitespace-nowrap"
              >
                View Details
              </button>

              <button
                @click="addToCart"
                :disabled="product.stock_quantity === 0"
                class="btn-primary px-4 py-2 text-sm whitespace-nowrap disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span v-if="isInCart">Update Cart</span>
                <span v-else>Add to Cart</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useRouter } from "vue-router";
import { useCartStore } from "@/stores/cart";
import { useProductsStore } from "@/stores/products";
import type { Product } from "@/types";

interface Props {
  product: Product;
}

const props = defineProps<Props>();
const router = useRouter();
const cartStore = useCartStore();
const productsStore = useProductsStore();

const isInCart = computed(() => cartStore.isInCart(props.product.id));
const cartQuantity = computed(() =>
  cartStore.getItemQuantity(props.product.id)
);

const getCategoryName = (categoryId: string) => {
  const category = productsStore.categories.find(c => c.id === categoryId);
  return category?.name || '';
};

const viewProduct = () => {
  router.push({ name: "product-detail", params: { id: props.product.id } });
};

const addToCart = () => {
  if (props.product.stock_quantity > 0) {
    cartStore.addItem(props.product, 1);
  }
};

const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement;
  target.src =
    "https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=400";
};
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
