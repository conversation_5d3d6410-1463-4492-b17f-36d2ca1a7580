import { supabase } from "@/lib/supabase";
import type {
  Review,
  ReviewInsert,
  ReviewUpdate,
} from "@/types";

export class ReviewService {
  /**
   * Get all approved reviews for a product
   */
  static async getProductReviews(productId: string): Promise<Review[]> {
    try {
      const { data, error } = await supabase
        .from("reviews")
        .select(`
          *,
          profiles (
            full_name
          )
        `)
        .eq("product_id", productId)
        .eq("is_approved", true)
        .order("created_at", { ascending: false });

      if (error) throw error;
      return data as Review[];
    } catch (error) {
      console.error("Error fetching product reviews:", error);
      throw error;
    }
  }

  /**
   * Get review statistics for a product
   */
  static async getProductReviewStats(productId: string): Promise<{
    averageRating: number;
    totalReviews: number;
    ratingDistribution: { [key: number]: number };
  }> {
    try {
      const { data, error } = await supabase
        .from("reviews")
        .select("rating")
        .eq("product_id", productId)
        .eq("is_approved", true);

      if (error) throw error;

      const reviews = data || [];
      const totalReviews = reviews.length;
      
      if (totalReviews === 0) {
        return {
          averageRating: 0,
          totalReviews: 0,
          ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 },
        };
      }

      const averageRating = reviews.reduce((sum, review) => sum + review.rating, 0) / totalReviews;
      
      const ratingDistribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
      reviews.forEach(review => {
        ratingDistribution[review.rating as keyof typeof ratingDistribution]++;
      });

      return {
        averageRating: Math.round(averageRating * 10) / 10,
        totalReviews,
        ratingDistribution,
      };
    } catch (error) {
      console.error("Error fetching product review stats:", error);
      throw error;
    }
  }

  /**
   * Create a new review
   */
  static async createReview(reviewData: ReviewInsert): Promise<Review> {
    try {
      const { data, error } = await supabase
        .from("reviews")
        .insert(reviewData)
        .select()
        .single();

      if (error) throw error;
      return data as Review;
    } catch (error) {
      console.error("Error creating review:", error);
      throw error;
    }
  }

  /**
   * Update a review
   */
  static async updateReview(id: string, reviewData: ReviewUpdate): Promise<Review> {
    try {
      const { data, error } = await supabase
        .from("reviews")
        .update(reviewData)
        .eq("id", id)
        .select()
        .single();

      if (error) throw error;
      return data as Review;
    } catch (error) {
      console.error("Error updating review:", error);
      throw error;
    }
  }

  /**
   * Delete a review
   */
  static async deleteReview(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from("reviews")
        .delete()
        .eq("id", id);

      if (error) throw error;
    } catch (error) {
      console.error("Error deleting review:", error);
      throw error;
    }
  }

  /**
   * Check if user can review a product (has purchased and delivered)
   */
  static async canUserReviewProduct(productId: string, userId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from("order_items")
        .select(`
          orders (
            user_id,
            status
          )
        `)
        .eq("product_id", productId);

      if (error) throw error;

      return data?.some(item => 
        item.orders?.user_id === userId && 
        item.orders?.status === 'delivered'
      ) || false;
    } catch (error) {
      console.error("Error checking if user can review product:", error);
      return false;
    }
  }

  /**
   * Check if user has already reviewed a product
   */
  static async hasUserReviewedProduct(productId: string, userId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from("reviews")
        .select("id")
        .eq("product_id", productId)
        .eq("user_id", userId)
        .single();

      if (error && error.code !== "PGRST116") throw error;
      return !!data;
    } catch (error) {
      console.error("Error checking if user has reviewed product:", error);
      return false;
    }
  }

  /**
   * Get user's review for a product
   */
  static async getUserReviewForProduct(productId: string, userId: string): Promise<Review | null> {
    try {
      const { data, error } = await supabase
        .from("reviews")
        .select("*")
        .eq("product_id", productId)
        .eq("user_id", userId)
        .single();

      if (error && error.code !== "PGRST116") throw error;
      return data as Review || null;
    } catch (error) {
      console.error("Error fetching user review for product:", error);
      return null;
    }
  }

  /**
   * Admin: Get all reviews (approved and pending)
   */
  static async getAllReviews(filters?: {
    isApproved?: boolean;
    productId?: string;
  }): Promise<Review[]> {
    try {
      let query = supabase
        .from("reviews")
        .select(`
          *,
          profiles (
            full_name,
            email
          ),
          products (
            name
          )
        `)
        .order("created_at", { ascending: false });

      if (filters?.isApproved !== undefined) {
        query = query.eq("is_approved", filters.isApproved);
      }

      if (filters?.productId) {
        query = query.eq("product_id", filters.productId);
      }

      const { data, error } = await query;

      if (error) throw error;
      return data as Review[];
    } catch (error) {
      console.error("Error fetching all reviews:", error);
      throw error;
    }
  }

  /**
   * Admin: Approve a review
   */
  static async approveReview(id: string): Promise<Review> {
    return this.updateReview(id, { is_approved: true });
  }

  /**
   * Admin: Reject a review
   */
  static async rejectReview(id: string): Promise<void> {
    return this.deleteReview(id);
  }

  /**
   * Admin: Feature a review
   */
  static async featureReview(id: string, featured: boolean = true): Promise<Review> {
    return this.updateReview(id, { is_featured: featured });
  }
}
