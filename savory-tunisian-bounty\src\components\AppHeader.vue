<template>
  
  <header class="header-glass shadow-md sticky top-0 z-40 border-b border-gray-100">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <!-- Logo -->
        <div class="flex items-center">
          <router-link to="/" class="flex items-center space-x-3 group">
            <div class="text-2xl transition-transform group-hover:scale-110">🌿</div>
            <span class="text-xl font-serif font-bold text-primary-600 group-hover:text-primary-700 transition-colors">
              Savory Tunisian Bounty
            </span>
          </router-link>
        </div>

        <!-- Desktop Navigation -->
        <nav class="hidden md:flex items-center space-x-8">
          <router-link
            v-for="(item, index) in navItems" 
            :key="index"
            :to="item.path"
            class="text-gray-700 hover:text-primary-600 font-medium transition-colors relative py-2 group"
            active-class="text-primary-600"
          >
            {{ item.label }}
            <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-primary-500 group-hover:w-full transition-all duration-300"></span>
          </router-link>
        </nav>

        <!-- Right side actions -->
        <div class="flex items-center space-x-5">
          <!-- Search (hidden on mobile) -->
          <div class="hidden lg:block">
            <div class="relative">
              <input
                v-model="searchQuery"
                type="text"
                placeholder="Search products..."
                class="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200"
                @keyup.enter="handleSearch"
              />
              <div
                class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
              >
                <!-- Heroicons - Search -->
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                </svg>
              </div>
            </div>
          </div>

          <!-- Cart -->
          <router-link
            to="/cart"
            class="relative p-2 text-gray-700 hover:text-primary-600 transition-colors rounded-full hover:bg-gray-100"
            aria-label="Shopping cart"
          >
            <!-- Heroicons - Shopping Cart -->
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
            <span
              v-if="cartItemCount > 0"
              class="absolute -top-1 -right-1 bg-primary-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold shadow-sm"
            >
              {{ cartItemCount }}
            </span>
          </router-link>

          <!-- User menu -->
          <div v-if="isAuthenticated" class="relative" ref="userMenuRef">
            <button
              @click="showUserMenu = !showUserMenu"
              class="flex items-center space-x-2 p-2 text-gray-700 hover:text-primary-600 transition-colors rounded-full hover:bg-gray-100"
              aria-label="User menu"
            >
              <!-- Heroicons - User -->
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              <!-- Heroicons - Chevron Down -->
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 transition-transform duration-200" :class="{'rotate-180': showUserMenu}" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
              </svg>
            </button>

            <!-- User dropdown -->
            <transition
              enter-active-class="transition ease-out duration-200"
              enter-from-class="transform opacity-0 scale-95"
              enter-to-class="transform opacity-100 scale-100"
              leave-active-class="transition ease-in duration-150"
              leave-from-class="transform opacity-100 scale-100"
              leave-to-class="transform opacity-0 scale-95"
            >
              <div
                v-if="showUserMenu"
                class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-100 ring-1 ring-black ring-opacity-5"
              >
                <router-link
                  v-for="(item, index) in userMenuItems"
                  :key="index"
                  :to="item.path"
                  class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-primary-600"
                  @click="showUserMenu = false"
                >
                  {{ item.label }}
                </router-link>
                <div v-if="authStore.isAdmin" class="border-t border-gray-100 my-1"></div>
                <router-link
                  v-if="authStore.isAdmin"
                  to="/admin/dashboard"
                  class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-primary-600 font-medium"
                  @click="showUserMenu = false"
                >
                  Admin Dashboard
                </router-link>
                <div class="border-t border-gray-100 my-1"></div>
                <button
                  @click="handleSignOut"
                  class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 hover:text-red-700"
                >
                  Sign Out
                </button>
              </div>
            </transition>
          </div>

          <!-- Auth buttons -->
          <div v-else class="flex items-center space-x-3">
            <router-link
              to="/login"
              class="text-gray-700 hover:text-primary-600 font-medium transition-colors px-3 py-2 rounded-md hover:bg-gray-50"
            >
              Sign In
            </router-link>
            <router-link to="/register" class="btn-primary px-4 py-2 shadow-sm hover:shadow">
              Sign Up
            </router-link>
          </div>

          <!-- Mobile menu button -->
          <button
            @click="showMobileMenu = !showMobileMenu"
            class="md:hidden p-2 text-gray-700 hover:text-primary-600 transition-colors rounded-md hover:bg-gray-100"
            aria-label="Toggle mobile menu"
          >
            <!-- Heroicons - Menu (or X when open) -->
            <svg v-if="!showMobileMenu" xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
            <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>

      <!-- Mobile menu -->
      <transition
        enter-active-class="transition-all duration-300 ease-out"
        enter-from-class="opacity-0 max-h-0"
        enter-to-class="opacity-100 max-h-96"
        leave-active-class="transition-all duration-200 ease-in"
        leave-from-class="opacity-100 max-h-96"
        leave-to-class="opacity-0 max-h-0"
      >
        <div
          v-if="showMobileMenu"
          class="md:hidden border-t border-gray-200 py-3 overflow-hidden"
        >
          <div class="space-y-1">
            <router-link
              v-for="(item, index) in navItems"
              :key="index"
              :to="item.path"
              class="block px-4 py-2 text-base font-medium text-gray-700 hover:text-primary-600 hover:bg-gray-50 rounded-md"
              active-class="text-primary-600 bg-primary-50"
              @click="showMobileMenu = false"
            >
              {{ item.label }}
            </router-link>
            
            <!-- Mobile divider -->
            <div class="border-t border-gray-200 my-2"></div>

            <!-- Mobile search -->
            <div class="px-4 py-2">
              <div class="relative">
                <input
                  v-model="searchQuery"
                  type="text"
                  placeholder="Search products..."
                  class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  @keyup.enter="handleSearch"
                />
                <div
                  class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                >
                  <!-- Heroicons - Search -->
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
      </transition>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from "vue";
import { useRouter } from "vue-router";
import { useAuthStore } from "@/stores/auth";

interface Props {
  isAuthenticated: boolean;
  cartItemCount: number;
}

defineProps<Props>();

const router = useRouter();
const authStore = useAuthStore();

const searchQuery = ref("");
const showUserMenu = ref(false);
const showMobileMenu = ref(false);
const userMenuRef = ref<HTMLElement>();

// Navigation items
const navItems = [
  { label: "Home", path: "/" },
  { label: "Products", path: "/products" },
  { label: "Producer Stories", path: "/producer-stories" },
  { label: "About", path: "#" },
  { label: "Contact", path: "#" }
];

// User menu items
const userMenuItems = [
  { label: "Profile", path: "/profile" },
  { label: "Order History", path: "/orders" }
];

const handleSearch = () => {
  if (searchQuery.value.trim()) {
    router.push({ name: "products", query: { search: searchQuery.value } });
    searchQuery.value = "";
    showMobileMenu.value = false;
  }
};

const handleSignOut = async () => {
  await authStore.signOut();
  showUserMenu.value = false;
  router.push("/");
};

// Close dropdowns when clicking outside
const handleClickOutside = (event: Event) => {
  if (userMenuRef.value && !userMenuRef.value.contains(event.target as Node)) {
    showUserMenu.value = false;
  }
};

onMounted(() => {
  document.addEventListener("click", handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener("click", handleClickOutside);
});
</script>
<style>
    .header-glass {
      background-color: rgba(255, 255, 255, 0.80);
      backdrop-filter: blur(8px);
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03);
      transition: background-color 0.3s ease, box-shadow 0.3s ease;
    }

  </style>




