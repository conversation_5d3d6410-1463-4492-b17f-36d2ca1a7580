<template>
  <div class="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow duration-200">
    <div class="flex items-center space-x-4">
      <!-- Product Image -->
      <div class="flex-shrink-0">
        <img
          :src="product.image_url || '/placeholder-product.jpg'"
          :alt="product.name"
          class="w-20 h-20 object-cover rounded-lg"
          @error="handleImageError"
        />
      </div>

      <!-- Product Info -->
      <div class="flex-1 min-w-0">
        <div class="flex items-start justify-between">
          <div class="flex-1">
            <h3 class="text-lg font-semibold text-gray-900 mb-1">
              {{ product.name }}
            </h3>
            
            <p class="text-gray-600 text-sm mb-2 line-clamp-1">
              {{ product.description }}
            </p>

            <!-- Product Badges -->
            <div class="flex flex-wrap gap-1 mb-2">
              <span
                v-if="product.is_organic"
                class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800"
              >
                Organic
              </span>
              
              <span
                v-if="product.is_vegan"
                class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800"
              >
                Vegan
              </span>
              
              <span
                v-if="product.is_gluten_free"
                class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800"
              >
                Gluten Free
              </span>

              <span
                v-if="product.is_fair_trade"
                class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800"
              >
                Fair Trade
              </span>
            </div>

            <!-- Stock Info -->
            <div class="flex items-center text-sm text-gray-500">
              <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
              </svg>
              {{ product.stock_quantity }} in stock
            </div>
          </div>

          <!-- Price and Actions -->
          <div class="flex flex-col items-end space-y-3 ml-4">
            <div class="text-right">
              <div class="text-2xl font-bold text-primary-600">
                ${{ product.price.toFixed(2) }}
              </div>
            </div>

            <div class="flex space-x-2">
              <button 
                @click="viewProduct" 
                class="px-4 py-2 text-sm border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
              >
                View Details
              </button>

              <button
                @click="addToCart"
                :disabled="product.stock_quantity === 0"
                class="px-4 py-2 text-sm bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span v-if="isInCart">In Cart ({{ cartQuantity }})</span>
                <span v-else>Add to Cart</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useRouter } from "vue-router";
import { useCartStore } from "@/stores/cart";
import { useProductsStore } from "@/stores/products";
import type { Product } from "@/types";

interface Props {
  product: Product;
}

const props = defineProps<Props>();
const router = useRouter();
const cartStore = useCartStore();
const productsStore = useProductsStore();

const isInCart = computed(() => cartStore.isInCart(props.product.id));
const cartQuantity = computed(() =>
  cartStore.getItemQuantity(props.product.id)
);

const getCategoryName = (categoryId: string) => {
  const category = productsStore.categories.find(c => c.id === categoryId);
  return category?.name || '';
};

const viewProduct = () => {
  router.push({ name: "product-detail", params: { id: props.product.id } });
};

const addToCart = () => {
  if (props.product.stock_quantity > 0) {
    cartStore.addItem(props.product, 1);
  }
};

const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement;
  target.src =
    "https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=400";
};
</script>

<style scoped>
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
