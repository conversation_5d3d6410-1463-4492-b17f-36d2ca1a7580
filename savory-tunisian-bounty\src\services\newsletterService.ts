import { supabase } from "@/lib/supabase";
import type {
  NewsletterSubscription,
  NewsletterSubscriptionInsert,
  NewsletterSubscriptionUpdate,
} from "@/types";

export class NewsletterService {
  /**
   * Subscribe to newsletter
   */
  static async subscribe(email: string): Promise<NewsletterSubscription> {
    try {
      // Check if email already exists
      const { data: existing } = await supabase
        .from("newsletter_subscriptions")
        .select("*")
        .eq("email", email)
        .single();

      if (existing) {
        // If exists but unsubscribed, reactivate
        if (!existing.is_active) {
          const { data, error } = await supabase
            .from("newsletter_subscriptions")
            .update({
              is_active: true,
              subscribed_at: new Date().toISOString(),
              unsubscribed_at: null,
            })
            .eq("email", email)
            .select()
            .single();

          if (error) throw error;
          return data as NewsletterSubscription;
        } else {
          // Already subscribed
          return existing as NewsletterSubscription;
        }
      }

      // Create new subscription
      const subscriptionData: NewsletterSubscriptionInsert = {
        email,
        is_active: true,
      };

      const { data, error } = await supabase
        .from("newsletter_subscriptions")
        .insert(subscriptionData)
        .select()
        .single();

      if (error) throw error;
      return data as NewsletterSubscription;
    } catch (error) {
      console.error("Error subscribing to newsletter:", error);
      throw error;
    }
  }

  /**
   * Unsubscribe from newsletter
   */
  static async unsubscribe(email: string): Promise<void> {
    try {
      const { error } = await supabase
        .from("newsletter_subscriptions")
        .update({
          is_active: false,
          unsubscribed_at: new Date().toISOString(),
        })
        .eq("email", email);

      if (error) throw error;
    } catch (error) {
      console.error("Error unsubscribing from newsletter:", error);
      throw error;
    }
  }

  /**
   * Check if email is subscribed
   */
  static async isSubscribed(email: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from("newsletter_subscriptions")
        .select("is_active")
        .eq("email", email)
        .single();

      if (error && error.code !== "PGRST116") throw error;
      return data?.is_active || false;
    } catch (error) {
      console.error("Error checking newsletter subscription:", error);
      return false;
    }
  }

  /**
   * Admin: Get all newsletter subscriptions
   */
  static async getAllSubscriptions(filters?: {
    isActive?: boolean;
  }): Promise<NewsletterSubscription[]> {
    try {
      let query = supabase
        .from("newsletter_subscriptions")
        .select("*")
        .order("subscribed_at", { ascending: false });

      if (filters?.isActive !== undefined) {
        query = query.eq("is_active", filters.isActive);
      }

      const { data, error } = await query;

      if (error) throw error;
      return data as NewsletterSubscription[];
    } catch (error) {
      console.error("Error fetching newsletter subscriptions:", error);
      throw error;
    }
  }

  /**
   * Admin: Get subscription statistics
   */
  static async getSubscriptionStats(): Promise<{
    totalSubscriptions: number;
    activeSubscriptions: number;
    unsubscribedCount: number;
    recentSubscriptions: number; // Last 30 days
  }> {
    try {
      const { data, error } = await supabase
        .from("newsletter_subscriptions")
        .select("is_active, subscribed_at");

      if (error) throw error;

      const subscriptions = data || [];
      const totalSubscriptions = subscriptions.length;
      const activeSubscriptions = subscriptions.filter(sub => sub.is_active).length;
      const unsubscribedCount = totalSubscriptions - activeSubscriptions;

      // Count recent subscriptions (last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      
      const recentSubscriptions = subscriptions.filter(sub => 
        new Date(sub.subscribed_at) >= thirtyDaysAgo
      ).length;

      return {
        totalSubscriptions,
        activeSubscriptions,
        unsubscribedCount,
        recentSubscriptions,
      };
    } catch (error) {
      console.error("Error fetching subscription stats:", error);
      throw error;
    }
  }

  /**
   * Admin: Export active subscribers (for email marketing)
   */
  static async exportActiveSubscribers(): Promise<string[]> {
    try {
      const { data, error } = await supabase
        .from("newsletter_subscriptions")
        .select("email")
        .eq("is_active", true)
        .order("subscribed_at", { ascending: false });

      if (error) throw error;
      return (data || []).map(sub => sub.email);
    } catch (error) {
      console.error("Error exporting active subscribers:", error);
      throw error;
    }
  }

  /**
   * Admin: Delete subscription permanently
   */
  static async deleteSubscription(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from("newsletter_subscriptions")
        .delete()
        .eq("id", id);

      if (error) throw error;
    } catch (error) {
      console.error("Error deleting newsletter subscription:", error);
      throw error;
    }
  }
}
