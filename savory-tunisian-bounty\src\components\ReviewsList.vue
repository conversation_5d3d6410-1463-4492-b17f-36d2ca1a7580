<template>
  <div class="space-y-6">
    <!-- Reviews Summary -->
    <div v-if="reviewStats" class="bg-white rounded-lg shadow-md p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900">Customer Reviews</h3>
        <div class="flex items-center space-x-2">
          <div class="flex items-center">
            <span class="text-2xl font-bold text-gray-900">
              {{ reviewStats.averageRating.toFixed(1) }}
            </span>
            <div class="flex ml-2">
              <span
                v-for="star in 5"
                :key="star"
                class="text-lg"
                :class="star <= Math.round(reviewStats.averageRating) ? 'text-yellow-400' : 'text-gray-300'"
              >
                ★
              </span>
            </div>
          </div>
          <span class="text-gray-600">
            ({{ reviewStats.totalReviews }} {{ reviewStats.totalReviews === 1 ? 'review' : 'reviews' }})
          </span>
        </div>
      </div>

      <!-- Rating Distribution -->
      <div v-if="reviewStats.totalReviews > 0" class="space-y-2">
        <div
          v-for="rating in [5, 4, 3, 2, 1]"
          :key="rating"
          class="flex items-center space-x-3"
        >
          <span class="text-sm text-gray-600 w-8">{{ rating }}★</span>
          <div class="flex-1 bg-gray-200 rounded-full h-2">
            <div
              class="bg-yellow-400 h-2 rounded-full"
              :style="{
                width: `${(reviewStats.ratingDistribution[rating] / reviewStats.totalReviews) * 100}%`
              }"
            ></div>
          </div>
          <span class="text-sm text-gray-600 w-8">
            {{ reviewStats.ratingDistribution[rating] }}
          </span>
        </div>
      </div>
    </div>

    <!-- Write Review Button -->
    <div v-if="canWriteReview && !showReviewForm" class="text-center">
      <button
        @click="showReviewForm = true"
        class="btn-primary px-6 py-3"
      >
        {{ userReview ? 'Update Your Review' : 'Write a Review' }}
      </button>
    </div>

    <!-- Review Form -->
    <ReviewForm
      v-if="showReviewForm"
      :product-id="productId"
      :existing-review="userReview"
      @success="handleReviewSuccess"
      @cancel="showReviewForm = false"
    />

    <!-- Reviews List -->
    <div v-if="reviews.length > 0" class="space-y-4">
      <h4 class="text-md font-medium text-gray-900">
        All Reviews ({{ reviews.length }})
      </h4>
      
      <div
        v-for="review in reviews"
        :key="review.id"
        class="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
      >
        <!-- Review Header -->
        <div class="flex items-start justify-between mb-3">
          <div>
            <div class="flex items-center space-x-2 mb-1">
              <span class="font-medium text-gray-900">
                {{ review.profiles?.full_name || 'Anonymous' }}
              </span>
              <div class="flex">
                <span
                  v-for="star in 5"
                  :key="star"
                  class="text-sm"
                  :class="star <= review.rating ? 'text-yellow-400' : 'text-gray-300'"
                >
                  ★
                </span>
              </div>
            </div>
            <p class="text-sm text-gray-500">
              {{ formatDate(review.created_at) }}
            </p>
          </div>
          <div v-if="review.is_featured" class="flex items-center">
            <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">
              Featured
            </span>
          </div>
        </div>

        <!-- Review Title -->
        <h5 v-if="review.title" class="font-medium text-gray-900 mb-2">
          {{ review.title }}
        </h5>

        <!-- Review Comment -->
        <p v-if="review.comment" class="text-gray-700 leading-relaxed">
          {{ review.comment }}
        </p>

        <!-- User's own review actions -->
        <div v-if="isUserReview(review)" class="mt-4 pt-4 border-t border-gray-200">
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-600">
              {{ review.is_approved ? 'Published' : 'Pending approval' }}
            </span>
            <button
              v-if="!review.is_approved"
              @click="editReview(review)"
              class="text-primary-600 hover:text-primary-700 text-sm font-medium"
            >
              Edit Review
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- No Reviews Message -->
    <div v-else-if="!loading" class="text-center py-8">
      <p class="text-gray-500 mb-4">No reviews yet for this product.</p>
      <p v-if="!canWriteReview && authStore.isAuthenticated" class="text-sm text-gray-400">
        Purchase this product to write the first review!
      </p>
      <p v-else-if="!authStore.isAuthenticated" class="text-sm text-gray-400">
        <router-link to="/auth/login" class="text-primary-600 hover:text-primary-700">
          Sign in
        </router-link>
        to write a review.
      </p>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="text-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
      <p class="text-gray-500 mt-2">Loading reviews...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { ReviewService } from '@/services/reviewService';
import { useAuthStore } from '@/stores/auth';
import ReviewForm from './ReviewForm.vue';
import type { Review } from '@/types';

interface Props {
  productId: string;
}

const props = defineProps<Props>();

const authStore = useAuthStore();

const loading = ref(false);
const reviews = ref<Review[]>([]);
const reviewStats = ref<{
  averageRating: number;
  totalReviews: number;
  ratingDistribution: { [key: number]: number };
} | null>(null);
const userReview = ref<Review | null>(null);
const canWriteReview = ref(false);
const showReviewForm = ref(false);

const isUserReview = (review: Review) => {
  return authStore.user?.id === review.user_id;
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
};

const loadReviews = async () => {
  loading.value = true;
  try {
    const [reviewsData, statsData] = await Promise.all([
      ReviewService.getProductReviews(props.productId),
      ReviewService.getProductReviewStats(props.productId),
    ]);

    reviews.value = reviewsData;
    reviewStats.value = statsData;
  } catch (error) {
    console.error('Error loading reviews:', error);
  } finally {
    loading.value = false;
  }
};

const checkUserReviewStatus = async () => {
  if (!authStore.user) return;

  try {
    const [canReview, existingReview] = await Promise.all([
      ReviewService.canUserReviewProduct(props.productId, authStore.user.id),
      ReviewService.getUserReviewForProduct(props.productId, authStore.user.id),
    ]);

    canWriteReview.value = canReview;
    userReview.value = existingReview;
  } catch (error) {
    console.error('Error checking user review status:', error);
  }
};

const handleReviewSuccess = (review: Review) => {
  showReviewForm.value = false;
  userReview.value = review;
  
  // Refresh reviews to show updated stats
  loadReviews();
};

const editReview = (review: Review) => {
  userReview.value = review;
  showReviewForm.value = true;
};

onMounted(() => {
  loadReviews();
  if (authStore.isAuthenticated) {
    checkUserReviewStatus();
  }
});
</script>

<style scoped>
.btn-primary {
  @apply bg-primary-600 text-white hover:bg-primary-700 rounded-md font-medium transition-colors;
}
</style>
