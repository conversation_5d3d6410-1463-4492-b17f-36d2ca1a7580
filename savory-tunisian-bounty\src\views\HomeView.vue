<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Hero Section -->
    <section
      class="bg-gradient-to-r from-primary-600 to-primary-800 text-white"
    >
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
        <div class="text-center">
          <h1 class="text-4xl md:text-6xl font-serif font-bold mb-6">
            Savory Tunisian Bounty
          </h1>
          <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
            Discover the authentic flavors of Tunisia with our premium selection
            of traditional spices, olive oils, and artisanal products.
          </p>
          <div class="space-x-4">
            <router-link
              to="/products"
              class="btn-secondary inline-block text-lg px-8 py-3"
            >
              Shop Now
            </router-link>
            <button
              class="btn-outline inline-block text-lg px-8 py-3 border-white text-white hover:bg-white hover:text-primary-600"
            >
              Learn More
            </button>
          </div>
        </div>
      </div>
    </section>

    <!-- Featured Categories -->
    <section class="py-16">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-serif font-bold text-gray-900 mb-4">
            Explore Our Categories
          </h2>
          <p class="text-lg text-gray-600 max-w-2xl mx-auto">
            From aromatic spices to golden olive oils, discover the treasures of
            Tunisian cuisine
          </p>
        </div>

        <div
          class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          v-if="!categoriesLoading"
        >
          <div
            v-for="category in categories"
            :key="category.id"
            class="card hover:shadow-lg transition-shadow duration-300 cursor-pointer"
            @click="goToCategory(category.id)"
          >
            <div
              class="h-48 bg-gradient-to-br from-primary-100 to-primary-200 flex items-center justify-center"
            >
              <div class="text-6xl">🌿</div>
            </div>
            <div class="p-6">
              <h3 class="text-xl font-semibold text-gray-900 mb-2">
                {{ category.name }}
              </h3>
              <p class="text-gray-600">{{ category.description }}</p>
            </div>
          </div>
        </div>

        <div
          v-else
          class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          <div v-for="i in 6" :key="i" class="card animate-pulse">
            <div class="h-48 bg-gray-200"></div>
            <div class="p-6">
              <div class="h-6 bg-gray-200 rounded mb-2"></div>
              <div class="h-4 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Featured Producer Stories -->
    <section class="py-16 bg-amber-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-serif font-bold text-gray-900 mb-4">
            Meet Our Producers
          </h2>
          <p class="text-lg text-gray-600 max-w-2xl mx-auto">
            Discover the passionate artisans behind our authentic Tunisian
            specialties
          </p>
        </div>

        <div
          class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          v-if="!storiesLoading && featuredStories.length > 0"
        >
          <ProducerStoryCard
            v-for="story in featuredStories"
            :key="story.id"
            :story="story"
          />
        </div>

        <div
          v-else-if="storiesLoading"
          class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          <div v-for="i in 3" :key="i" class="card animate-pulse">
            <div class="h-48 bg-gray-200"></div>
            <div class="p-6">
              <div class="h-6 bg-gray-200 rounded mb-2"></div>
              <div class="h-4 bg-gray-200 rounded mb-2"></div>
              <div class="h-4 bg-gray-200 rounded w-3/4"></div>
            </div>
          </div>
        </div>

        <div
          class="text-center mt-12"
          v-if="!storiesLoading && featuredStories.length > 0"
        >
          <router-link
            to="/producer-stories"
            class="btn-outline text-lg px-8 py-3"
          >
            View All Stories
          </router-link>
        </div>
      </div>
    </section>

    <!-- Featured Products -->
    <section class="py-16 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-serif font-bold text-gray-900 mb-4">
            Featured Products
          </h2>
          <p class="text-lg text-gray-600 max-w-2xl mx-auto">
            Hand-picked selections from our finest Tunisian producers
          </p>
        </div>

        <div
          class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6"
          v-if="!productsLoading"
        >
          <ProductCard
            v-for="product in featuredProducts"
            :key="product.id"
            :product="product"
          />
        </div>

        <div
          v-else
          class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6"
        >
          <div v-for="i in 4" :key="i" class="card animate-pulse">
            <div class="h-48 bg-gray-200"></div>
            <div class="p-4">
              <div class="h-6 bg-gray-200 rounded mb-2"></div>
              <div class="h-4 bg-gray-200 rounded mb-2"></div>
              <div class="h-6 bg-gray-200 rounded w-20"></div>
            </div>
          </div>
        </div>

        <div class="text-center mt-12">
          <router-link to="/products" class="btn-primary text-lg px-8 py-3">
            View All Products
          </router-link>
        </div>
      </div>
    </section>

    <!-- Newsletter Signup -->
    <section class="py-16 bg-gray-50">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <NewsletterSignup />
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { useRouter } from "vue-router";
import { useProductsStore } from "@/stores/products";
import { useProducerStoriesStore } from "@/stores/producerStories";
import ProductCard from "@/components/ProductCard.vue";
import ProducerStoryCard from "@/components/ProducerStoryCard.vue";
import NewsletterSignup from "@/components/NewsletterSignup.vue";

const router = useRouter();
const productsStore = useProductsStore();
const producerStoriesStore = useProducerStoriesStore();

const categoriesLoading = ref(true);
const productsLoading = ref(true);
const storiesLoading = ref(true);

const categories = computed(() => productsStore.categories);
const featuredProducts = computed(() =>
  productsStore.products.slice(0, 4).map((product) => ({
    ...product,
    images: product.images ? [...product.images] : null,
  }))
);
const featuredStories = computed(() =>
  producerStoriesStore.featuredStories.slice(0, 3)
);

const goToCategory = (categoryId: string) => {
  router.push({ name: "products", query: { category: categoryId } });
};

onMounted(async () => {
  try {
    // Load data in parallel
    await Promise.all([
      productsStore.fetchCategories().then(() => {
        categoriesLoading.value = false;
      }),
      productsStore.fetchProducts().then(() => {
        productsLoading.value = false;
      }),
      producerStoriesStore.fetchFeaturedStories().then(() => {
        storiesLoading.value = false;
      }),
    ]);
  } catch (error) {
    console.error("Error loading home page data:", error);
    categoriesLoading.value = false;
    productsLoading.value = false;
    storiesLoading.value = false;
  }
});
</script>
