<template>
  <div class="relative">
    <!-- Carousel Container -->
    <div class="overflow-hidden rounded-xl">
      <div
        ref="carouselContainer"
        class="flex transition-transform duration-500 ease-in-out"
        :style="{ transform: `translateX(-${currentSlide * slideWidth}%)` }"
        @touchstart="handleTouchStart"
        @touchmove="handleTouchMove"
        @touchend="handleTouchEnd"
      >
        <div
          v-for="(category, index) in categories"
          :key="category.id"
          class="flex-shrink-0 px-2"
          :class="slideClass"
        >
          <div
            class="card hover:shadow-lg transition-all duration-300 cursor-pointer group h-full"
            @click="$emit('categoryClick', category.id)"
            @keydown.enter="$emit('categoryClick', category.id)"
            @keydown.space.prevent="$emit('categoryClick', category.id)"
            tabindex="0"
            :aria-label="`View ${category.name} category`"
          >
            <!-- Category Image with 16:9 aspect ratio -->
            <div
              class="relative aspect-video bg-gradient-to-br from-primary-100 to-primary-200 overflow-hidden"
            >
              <img
                v-if="category.image_url"
                :src="category.image_url"
                :alt="category.name"
                class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                @error="handleImageError"
                loading="lazy"
              />
              <div
                v-else
                class="w-full h-full flex items-center justify-center bg-gradient-to-br from-primary-100 to-primary-200"
              >
                <svg
                  class="w-16 h-16 text-primary-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"
                  />
                </svg>
              </div>

              <!-- Loading overlay -->
              <div
                v-if="imageLoading[index]"
                class="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center"
              >
                <div
                  class="w-8 h-8 border-2 border-primary-500 border-t-transparent rounded-full animate-spin"
                ></div>
              </div>
            </div>

            <!-- Category Info -->
            <div class="p-6">
              <h3
                class="text-xl font-semibold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors"
              >
                {{ category.name }}
              </h3>
              <p class="text-gray-600 line-clamp-2">
                {{
                  category.description ||
                  "Discover authentic Tunisian specialties"
                }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Navigation Arrows -->
    <button
      v-if="categories.length > slidesToShow"
      @click="previousSlide"
      @keydown.enter="previousSlide"
      @keydown.space.prevent="previousSlide"
      class="nav-button absolute left-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white text-gray-800 rounded-full p-3 shadow-lg hover:scale-110 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 z-10"
      :class="{ 'opacity-50 cursor-not-allowed': currentSlide === 0 }"
      :disabled="currentSlide === 0"
      aria-label="Previous categories"
    >
      <svg
        class="w-5 h-5"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M15 19l-7-7 7-7"
        />
      </svg>
    </button>

    <button
      v-if="categories.length > slidesToShow"
      @click="nextSlide"
      @keydown.enter="nextSlide"
      @keydown.space.prevent="nextSlide"
      class="nav-button absolute right-4 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white text-gray-800 rounded-full p-3 shadow-lg hover:scale-110 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 z-10"
      :class="{ 'opacity-50 cursor-not-allowed': currentSlide >= maxSlide }"
      :disabled="currentSlide >= maxSlide"
      aria-label="Next categories"
    >
      <svg
        class="w-5 h-5"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M9 5l7 7-7 7"
        />
      </svg>
    </button>

    <!-- Pagination Dots -->
    <div
      v-if="categories.length > slidesToShow"
      class="flex justify-center space-x-2 mt-6"
      role="tablist"
      aria-label="Category carousel pagination"
    >
      <button
        v-for="(dot, index) in totalDots"
        :key="index"
        @click="goToSlide(index)"
        @keydown.enter="goToSlide(index)"
        @keydown.space.prevent="goToSlide(index)"
        class="pagination-dot w-3 h-3 rounded-full focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
        :class="[
          currentSlide === index
            ? 'bg-primary-600 active'
            : 'bg-gray-300 hover:bg-gray-400',
        ]"
        :aria-label="`Go to slide ${index + 1}`"
        :aria-selected="currentSlide === index"
        role="tab"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from "vue";
import type { Category } from "@/types";

interface Props {
  categories: Category[];
  autoSlide?: boolean;
  autoSlideInterval?: number;
}

const props = withDefaults(defineProps<Props>(), {
  autoSlide: true,
  autoSlideInterval: 5000,
});

const emit = defineEmits<{
  categoryClick: [categoryId: string];
}>();

// Reactive state
const currentSlide = ref(0);
const carouselContainer = ref<HTMLElement>();
const imageLoading = ref<Record<number, boolean>>({});
const isHovered = ref(false);
const autoSlideTimer = ref<NodeJS.Timeout>();

// Touch handling
const touchStartX = ref(0);
const touchEndX = ref(0);
const minSwipeDistance = 50;

// Responsive breakpoints
const slidesToShow = computed(() => {
  if (typeof window === "undefined") return 3;
  const width = window.innerWidth;
  if (width < 640) return 1; // mobile
  if (width < 1024) return 2; // tablet
  return 3; // desktop
});

const slideWidth = computed(() => 100 / slidesToShow.value);
const slideClass = computed(() => {
  const width = slidesToShow.value;
  if (width === 1) return "w-full";
  if (width === 2) return "w-1/2";
  return "w-1/3";
});

const maxSlide = computed(() =>
  Math.max(0, props.categories.length - slidesToShow.value)
);

const totalDots = computed(() => maxSlide.value + 1);

// Navigation methods
const nextSlide = () => {
  if (currentSlide.value < maxSlide.value) {
    currentSlide.value++;
  } else if (props.autoSlide) {
    currentSlide.value = 0; // Loop back to start
  }
};

const previousSlide = () => {
  if (currentSlide.value > 0) {
    currentSlide.value--;
  } else if (props.autoSlide) {
    currentSlide.value = maxSlide.value; // Loop to end
  }
};

const goToSlide = (index: number) => {
  if (index >= 0 && index <= maxSlide.value) {
    currentSlide.value = index;
  }
};

// Auto-slide functionality
const startAutoSlide = () => {
  if (props.autoSlide && props.categories.length > slidesToShow.value) {
    autoSlideTimer.value = setInterval(() => {
      if (!isHovered.value) {
        nextSlide();
      }
    }, props.autoSlideInterval);
  }
};

const stopAutoSlide = () => {
  if (autoSlideTimer.value) {
    clearInterval(autoSlideTimer.value);
    autoSlideTimer.value = undefined;
  }
};

// Touch event handlers
const handleTouchStart = (e: TouchEvent) => {
  touchStartX.value = e.changedTouches[0].screenX;
};

const handleTouchMove = (e: TouchEvent) => {
  e.preventDefault(); // Prevent scrolling
};

const handleTouchEnd = (e: TouchEvent) => {
  touchEndX.value = e.changedTouches[0].screenX;
  handleSwipe();
};

const handleSwipe = () => {
  const swipeDistance = touchStartX.value - touchEndX.value;

  if (Math.abs(swipeDistance) > minSwipeDistance) {
    if (swipeDistance > 0) {
      nextSlide(); // Swipe left - next slide
    } else {
      previousSlide(); // Swipe right - previous slide
    }
  }
};

// Image error handling
const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement;
  target.src =
    "https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=400";
};

// Keyboard navigation
const handleKeydown = (e: KeyboardEvent) => {
  if (
    e.target === carouselContainer.value ||
    carouselContainer.value?.contains(e.target as Node)
  ) {
    switch (e.key) {
      case "ArrowLeft":
        e.preventDefault();
        previousSlide();
        break;
      case "ArrowRight":
        e.preventDefault();
        nextSlide();
        break;
    }
  }
};

// Mouse hover handlers
const handleMouseEnter = () => {
  isHovered.value = true;
};

const handleMouseLeave = () => {
  isHovered.value = false;
};

// Lifecycle
onMounted(async () => {
  await nextTick();

  // Add event listeners
  document.addEventListener("keydown", handleKeydown);
  carouselContainer.value?.addEventListener("mouseenter", handleMouseEnter);
  carouselContainer.value?.addEventListener("mouseleave", handleMouseLeave);

  // Start auto-slide
  startAutoSlide();

  // Handle window resize
  window.addEventListener("resize", () => {
    // Reset to first slide on resize to avoid layout issues
    currentSlide.value = 0;
  });
});

onUnmounted(() => {
  stopAutoSlide();
  document.removeEventListener("keydown", handleKeydown);
  carouselContainer.value?.removeEventListener("mouseenter", handleMouseEnter);
  carouselContainer.value?.removeEventListener("mouseleave", handleMouseLeave);
});
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Ensure proper aspect ratio */
.aspect-video {
  aspect-ratio: 16 / 9;
}

/* Custom scrollbar for touch devices */
@media (hover: none) {
  .overflow-hidden {
    -webkit-overflow-scrolling: touch;
  }
}

/* Enhanced hover effects */
.card:hover {
  transform: translateY(-4px);
}

/* Focus styles for accessibility */
.card:focus {
  outline: 2px solid theme("colors.primary.500");
  outline-offset: 2px;
}

/* Navigation button hover effects */
.nav-button {
  backdrop-filter: blur(8px);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-button:hover {
  backdrop-filter: blur(12px);
  box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Pagination dots animation */
.pagination-dot {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.pagination-dot:hover {
  transform: scale(1.2);
}

.pagination-dot.active {
  transform: scale(1.1);
}

/* Mobile optimizations */
@media (max-width: 640px) {
  .nav-button {
    padding: 0.5rem;
  }

  .nav-button svg {
    width: 1rem;
    height: 1rem;
  }

  .pagination-dot {
    width: 0.5rem;
    height: 0.5rem;
  }
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  .card,
  .nav-button,
  .pagination-dot {
    transition: none;
  }
}
</style>
