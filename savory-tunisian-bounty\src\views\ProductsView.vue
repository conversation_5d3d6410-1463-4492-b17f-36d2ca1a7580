<template>
  <div class="min-h-screen bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <!-- Main Content Layout -->
      <div class="flex flex-col lg:flex-row gap-6">
        <!-- Sidebar Filters -->
        <div class="lg:w-80 flex-shrink-0">
          <!-- Mobile Filter Toggle -->
          <div class="lg:hidden mb-4">
            <button
              @click="showMobileFilters = !showMobileFilters"
              class="w-full flex items-center justify-between bg-white rounded-lg border border-gray-200 p-4 text-left"
            >
              <div class="flex items-center">
                <svg
                  class="w-5 h-5 mr-2 text-primary-600"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z"
                    clip-rule="evenodd"
                  />
                </svg>
                <span class="font-medium text-gray-900">Filters</span>
              </div>
              <div class="flex items-center space-x-2">
                <span
                  v-if="hasActiveFilters"
                  class="bg-primary-100 text-primary-800 text-xs px-2 py-1 rounded-full"
                >
                  {{ activeFiltersCount }}
                </span>
                <span class="text-sm text-gray-500">Clear All</span>
                <svg
                  class="w-5 h-5 text-gray-400 transform transition-transform"
                  :class="{ 'rotate-180': showMobileFilters }"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                    clip-rule="evenodd"
                  />
                </svg>
              </div>
            </button>
          </div>

          <!-- Filters Panel -->
          <div
            :class="{
              hidden: !showMobileFilters && !isDesktop,
              block: showMobileFilters || isDesktop,
            }"
          >
            <ProductFilters />
          </div>
        </div>

        <!-- Main Content Area -->
        <div class="flex-1 min-w-0">
          <!-- Search Bar -->
          <div class="bg-white rounded-lg border border-gray-200 p-4 mb-6">
            <div class="flex items-center space-x-4">
              <div class="flex-1 relative">
                <SearchBar
                  v-model="searchQuery"
                  @search="handleSearch"
                  placeholder="Search products, categories, or keywords..."
                />
              </div>

              <!-- View Toggle -->
              <div class="flex items-center space-x-3">
                <span class="text-sm text-gray-500">View:</span>
                <div class="flex bg-gray-100 rounded-lg p-1">
                  <button
                    @click="viewMode = 'grid'"
                    :class="[
                      'p-2 rounded-md transition-colors',
                      viewMode === 'grid'
                        ? 'bg-white shadow-sm text-primary-600'
                        : 'text-gray-500 hover:text-gray-700',
                    ]"
                  >
                    <svg
                      class="w-4 h-4"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
                      />
                    </svg>
                  </button>
                  <button
                    @click="viewMode = 'list'"
                    :class="[
                      'p-2 rounded-md transition-colors',
                      viewMode === 'list'
                        ? 'bg-white shadow-sm text-primary-600'
                        : 'text-gray-500 hover:text-gray-700',
                    ]"
                  >
                    <svg
                      class="w-4 h-4"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                        clip-rule="evenodd"
                      />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Results Header -->
          <div class="flex items-center justify-between mb-6">
            <div class="flex items-center space-x-4">
              <h2 class="text-lg font-semibold text-gray-900">
                <span class="text-primary-600 font-bold">{{
                  filteredProducts.length
                }}</span>
                product{{ filteredProducts.length !== 1 ? "s" : "" }} found
              </h2>
            </div>

            <div class="flex items-center space-x-4">
              <label class="text-sm text-gray-600">Sort by:</label>
              <select
                v-model="sortBy"
                @change="handleSort"
                class="bg-white border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                <option value="name">Name (A-Z)</option>
                <option value="name_desc">Name (Z-A)</option>
                <option value="price">Price (Low to High)</option>
                <option value="price_desc">Price (High to Low)</option>
                <option value="newest">Newest First</option>
                <option value="popular">Most Popular</option>
              </select>
            </div>
          </div>

          <!-- Products Display -->
          <div v-if="!loading && filteredProducts.length > 0">
            <!-- List View (Default) -->
            <div v-if="viewMode === 'list'" class="space-y-4">
              <ProductListItemCompact
                v-for="product in sortedProducts"
                :key="product.id"
                :product="product"
              />
            </div>

            <!-- Grid View -->
            <div
              v-else
              class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6"
            >
              <ProductCard
                v-for="product in sortedProducts"
                :key="product.id"
                :product="product"
              />
            </div>
          </div>

          <!-- Loading State -->
          <div v-else-if="loading">
            <div v-if="viewMode === 'list'" class="space-y-4">
              <div
                v-for="i in 8"
                :key="i"
                class="bg-white rounded-lg border border-gray-200 p-4 animate-pulse"
              >
                <div class="flex space-x-4">
                  <div class="h-20 w-20 bg-gray-200 rounded-lg"></div>
                  <div class="flex-1">
                    <div class="h-5 bg-gray-200 rounded mb-2"></div>
                    <div class="h-4 bg-gray-200 rounded mb-2 w-3/4"></div>
                    <div class="h-4 bg-gray-200 rounded w-1/2"></div>
                  </div>
                  <div class="h-8 bg-gray-200 rounded w-20"></div>
                </div>
              </div>
            </div>
            <div
              v-else
              class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6"
            >
              <div
                v-for="i in 9"
                :key="i"
                class="bg-white rounded-lg border border-gray-200 animate-pulse overflow-hidden"
              >
                <div class="h-48 bg-gray-200"></div>
                <div class="p-4">
                  <div class="h-5 bg-gray-200 rounded mb-2"></div>
                  <div class="h-4 bg-gray-200 rounded mb-2 w-3/4"></div>
                  <div class="h-6 bg-gray-200 rounded w-20"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- No Results State -->
          <div v-else class="text-center py-16">
            <div class="max-w-md mx-auto">
              <div
                class="w-20 h-20 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center"
              >
                <svg
                  class="w-10 h-10 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              </div>
              <h3 class="text-xl font-semibold text-gray-900 mb-3">
                No products found
              </h3>
              <p class="text-gray-600 mb-6">
                Try adjusting your search criteria or browse our categories
              </p>
              <div class="flex flex-col sm:flex-row gap-3 justify-center">
                <button
                  @click="productsStore.clearFilters"
                  class="btn-primary px-6 py-2"
                >
                  Clear Filters
                </button>
                <button
                  @click="
                    searchQuery = '';
                    handleSearch();
                  "
                  class="btn-outline px-6 py-2"
                >
                  Clear Search
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, onUnmounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useProductsStore } from "@/stores/products";
import ProductCard from "@/components/ProductCard.vue";
import SearchBar from "@/components/SearchBar.vue";
import ProductFilters from "@/components/ProductFilters.vue";
import ProductListItem from "@/components/ProductListItem.vue";
import ProductListItemCompact from "@/components/ProductListItemCompact.vue";

const route = useRoute();
const router = useRouter();
const productsStore = useProductsStore();

// Reactive state
const searchQuery = ref("");
const loading = ref(true);
const viewMode = ref<"grid" | "list">("list");
const sortBy = ref("name");
const showMobileFilters = ref(false);

// Responsive detection
const isDesktop = ref(window.innerWidth >= 1024);

const handleResize = () => {
  isDesktop.value = window.innerWidth >= 1024;
  if (isDesktop.value) {
    showMobileFilters.value = false;
  }
};

// Computed properties
const filteredProducts = computed(() => productsStore.filteredProducts);
const categories = computed(() => productsStore.categories);
const hasActiveFilters = computed(() => {
  const filters = productsStore.filters;
  return Object.keys(filters).some((key) => {
    const value = filters[key as keyof typeof filters];
    if (Array.isArray(value)) return value.length > 0;
    return value !== undefined && value !== null && value !== "";
  });
});

const activeFiltersCount = computed(() => {
  const filters = productsStore.filters;
  return Object.keys(filters).filter((key) => {
    const value = filters[key as keyof typeof filters];
    if (Array.isArray(value)) return value.length > 0;
    return value !== undefined && value !== null && value !== "";
  }).length;
});

const sortedProducts = computed(() => {
  const products = [...filteredProducts.value];

  switch (sortBy.value) {
    case "name":
      return products.sort((a, b) => a.name.localeCompare(b.name));
    case "name_desc":
      return products.sort((a, b) => b.name.localeCompare(a.name));
    case "price":
      return products.sort((a, b) => a.price - b.price);
    case "price_desc":
      return products.sort((a, b) => b.price - a.price);
    case "newest":
      return products.sort(
        (a, b) =>
          new Date(b.created_at || "").getTime() -
          new Date(a.created_at || "").getTime()
      );
    case "popular":
      // For now, sort by stock quantity as a proxy for popularity
      return products.sort(
        (a, b) => (b.stock_quantity || 0) - (a.stock_quantity || 0)
      );
    default:
      return products;
  }
});

// Methods
const handleSearch = () => {
  productsStore.updateFilter("search", searchQuery.value);
  updateURL();
};

const handleSort = () => {
  // Sorting is handled by the computed property
  updateURL();
};

const selectCategory = (categoryId: string) => {
  productsStore.updateFilter("category", categoryId);
  showMobileFilters.value = false;
};

const updateURL = () => {
  const query: Record<string, string> = {};

  if (searchQuery.value) {
    query.search = searchQuery.value;
  }

  if (sortBy.value !== "name") {
    query.sort = sortBy.value;
  }

  if (viewMode.value !== "grid") {
    query.view = viewMode.value;
  }

  router.push({ name: "products", query });
};

// Initialize from URL parameters
const initializeFromURL = () => {
  if (route.query.search) {
    searchQuery.value = route.query.search as string;
    productsStore.updateFilter("search", searchQuery.value);
  }

  if (route.query.sort) {
    sortBy.value = route.query.sort as string;
  }

  if (route.query.view) {
    viewMode.value = route.query.view as "grid" | "list";
  }
};

// Lifecycle hooks
onMounted(async () => {
  try {
    await productsStore.initialize();
    initializeFromURL();
    window.addEventListener("resize", handleResize);
  } catch (error) {
    console.error("Error loading products:", error);
  } finally {
    loading.value = false;
  }
});

onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
});

// Watch for route changes
watch(
  () => route.query,
  () => {
    initializeFromURL();
  },
  { deep: true }
);
</script>
