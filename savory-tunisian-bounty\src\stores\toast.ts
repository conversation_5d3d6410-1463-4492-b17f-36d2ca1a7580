import { defineStore } from "pinia";
import { ref } from "vue";

export interface Toast {
  id: string;
  message: string;
  type: "success" | "error" | "warning" | "info";
  duration?: number;
}

export const useToastStore = defineStore("toast", () => {
  const toasts = ref<Toast[]>([]);

  const addToast = (
    message: string,
    type: Toast["type"] = "info",
    duration: number = 3000
  ) => {
    const id = Date.now().toString();
    const toast: Toast = {
      id,
      message,
      type,
      duration,
    };

    toasts.value.push(toast);

    // Auto remove toast after duration
    if (duration > 0) {
      setTimeout(() => {
        removeToast(id);
      }, duration);
    }

    return id;
  };

  const removeToast = (id: string) => {
    const index = toasts.value.findIndex((toast) => toast.id === id);
    if (index > -1) {
      toasts.value.splice(index, 1);
    }
  };

  const clearAllToasts = () => {
    toasts.value = [];
  };

  // Convenience methods
  const success = (message: string, duration?: number) =>
    addToast(message, "success", duration);
  const error = (message: string, duration?: number) =>
    addToast(message, "error", duration);
  const warning = (message: string, duration?: number) =>
    addToast(message, "warning", duration);
  const info = (message: string, duration?: number) =>
    addToast(message, "info", duration);

  return {
    toasts,
    addToast,
    removeToast,
    clearAllToasts,
    success,
    error,
    warning,
    info,
  };
});
