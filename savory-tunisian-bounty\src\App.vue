<script setup lang="ts">
import { computed } from "vue";
import { useAuthStore } from "./stores/auth";
import { useCartStore } from "./stores/cart";
import AppHeader from "./components/AppHeader.vue";
import AppFooter from "./components/AppFooter.vue";
import ToastContainer from "./components/ToastContainer.vue";

const authStore = useAuthStore();
const cartStore = useCartStore();

const isAuthenticated = computed(() => authStore.isAuthenticated);
const cartItemCount = computed(() => cartStore.itemCount);
</script>

<template>
  <div id="app" class="min-h-screen flex flex-col">
    <AppHeader
      :is-authenticated="isAuthenticated"
      :cart-item-count="cartItemCount"
    />

    <main class="flex-1">
      <RouterView />
    </main>

    <AppFooter />

    <!-- Toast notifications -->
    <ToastContainer />
  </div>
</template>

<style>
/* Global styles are in main.css */
</style>
