<template>
  <div class="bg-primary-50 border border-primary-200 rounded-lg p-6">
    <div class="flex items-start space-x-4">
      <!-- Icon -->
      <div class="flex-shrink-0">
        <svg
          class="h-6 w-6 text-primary-600"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
          />
        </svg>
      </div>

      <div class="flex-1">
        <!-- Header -->
        <h3 class="text-lg font-semibold text-gray-900 mb-2">
          Stay Updated with Tunisian Delights
        </h3>
        <p class="text-gray-600 mb-4">
          Get the latest updates on new products, special offers, and authentic Tunisian recipes delivered to your inbox.
        </p>

        <!-- Success Message -->
        <div v-if="isSubscribed" class="flex items-center space-x-2 text-green-700">
          <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
            <path
              fill-rule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
              clip-rule="evenodd"
            />
          </svg>
          <span class="font-medium">Thank you for subscribing!</span>
        </div>

        <!-- Subscription Form -->
        <form v-else @submit.prevent="subscribe" class="space-y-3">
          <div class="flex flex-col sm:flex-row gap-3">
            <div class="flex-1">
              <label for="newsletter-email" class="sr-only">Email address</label>
              <input
                id="newsletter-email"
                v-model="email"
                type="email"
                required
                placeholder="Enter your email address"
                class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                :disabled="loading"
              />
              <p v-if="error" class="text-red-500 text-sm mt-1">
                {{ error }}
              </p>
            </div>
            <button
              type="submit"
              :disabled="loading || !email"
              class="px-6 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
            >
              {{ loading ? 'Subscribing...' : 'Subscribe' }}
            </button>
          </div>

          <!-- Privacy Notice -->
          <p class="text-xs text-gray-500">
            By subscribing, you agree to receive marketing emails from us. You can unsubscribe at any time.
            We respect your privacy and will never share your email address.
          </p>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { NewsletterService } from '@/services/newsletterService';
import { useToastStore } from '@/stores/toast';

const toastStore = useToastStore();

const email = ref('');
const loading = ref(false);
const error = ref('');
const isSubscribed = ref(false);

const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

const subscribe = async () => {
  error.value = '';

  if (!email.value) {
    error.value = 'Please enter your email address';
    return;
  }

  if (!validateEmail(email.value)) {
    error.value = 'Please enter a valid email address';
    return;
  }

  loading.value = true;

  try {
    await NewsletterService.subscribe(email.value);
    isSubscribed.value = true;
    toastStore.success('Successfully subscribed to our newsletter!');
    
    // Reset form
    email.value = '';
  } catch (error: any) {
    console.error('Error subscribing to newsletter:', error);
    
    // Handle specific error cases
    if (error.message?.includes('duplicate key')) {
      toastStore.info('You are already subscribed to our newsletter!');
      isSubscribed.value = true;
    } else {
      toastStore.error('Failed to subscribe. Please try again.');
    }
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
/* Component-specific styles if needed */
</style>
