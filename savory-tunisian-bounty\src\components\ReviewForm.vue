<template>
  <div class="bg-white rounded-lg shadow-md p-6">
    <h3 class="text-lg font-semibold text-gray-900 mb-4">
      {{ existingReview ? 'Update Your Review' : 'Write a Review' }}
    </h3>

    <form @submit.prevent="submitReview" class="space-y-4">
      <!-- Rating -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">
          Rating *
        </label>
        <div class="flex items-center space-x-1">
          <button
            v-for="star in 5"
            :key="star"
            type="button"
            @click="form.rating = star"
            class="text-2xl focus:outline-none transition-colors"
            :class="star <= form.rating ? 'text-yellow-400' : 'text-gray-300'"
          >
            ★
          </button>
        </div>
        <p v-if="errors.rating" class="text-red-500 text-sm mt-1">
          {{ errors.rating }}
        </p>
      </div>

      <!-- Title -->
      <div>
        <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
          Review Title
        </label>
        <input
          id="title"
          v-model="form.title"
          type="text"
          placeholder="Summarize your experience"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          maxlength="100"
        />
        <p class="text-gray-500 text-xs mt-1">
          {{ form.title?.length || 0 }}/100 characters
        </p>
      </div>

      <!-- Comment -->
      <div>
        <label for="comment" class="block text-sm font-medium text-gray-700 mb-2">
          Your Review
        </label>
        <textarea
          id="comment"
          v-model="form.comment"
          rows="4"
          placeholder="Share your thoughts about this product..."
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
          maxlength="1000"
        ></textarea>
        <p class="text-gray-500 text-xs mt-1">
          {{ form.comment?.length || 0 }}/1000 characters
        </p>
        <p v-if="errors.comment" class="text-red-500 text-sm mt-1">
          {{ errors.comment }}
        </p>
      </div>

      <!-- Submit Button -->
      <div class="flex justify-end space-x-3">
        <button
          type="button"
          @click="$emit('cancel')"
          class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          :disabled="loading || !isFormValid"
          class="px-6 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {{ loading ? 'Submitting...' : (existingReview ? 'Update Review' : 'Submit Review') }}
        </button>
      </div>
    </form>

    <!-- Info Message -->
    <div class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
      <p class="text-sm text-blue-700">
        <span class="font-medium">Note:</span> Your review will be reviewed by our team before being published.
        {{ existingReview && !existingReview.is_approved ? 'Your previous review is still pending approval.' : '' }}
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { ReviewService } from '@/services/reviewService';
import { useAuthStore } from '@/stores/auth';
import { useToastStore } from '@/stores/toast';
import type { Review } from '@/types';

interface Props {
  productId: string;
  existingReview?: Review | null;
}

interface Emits {
  (e: 'success', review: Review): void;
  (e: 'cancel'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const authStore = useAuthStore();
const toastStore = useToastStore();

const loading = ref(false);
const form = ref({
  rating: props.existingReview?.rating || 0,
  title: props.existingReview?.title || '',
  comment: props.existingReview?.comment || '',
});

const errors = ref<Record<string, string>>({});

const isFormValid = computed(() => {
  return form.value.rating > 0 && form.value.rating <= 5;
});

const validateForm = () => {
  errors.value = {};

  if (!form.value.rating || form.value.rating < 1 || form.value.rating > 5) {
    errors.value.rating = 'Please select a rating between 1 and 5 stars';
  }

  if (form.value.comment && form.value.comment.length > 1000) {
    errors.value.comment = 'Comment must be less than 1000 characters';
  }

  return Object.keys(errors.value).length === 0;
};

const submitReview = async () => {
  if (!validateForm()) return;
  if (!authStore.user) return;

  loading.value = true;

  try {
    let review: Review;

    if (props.existingReview) {
      // Update existing review
      review = await ReviewService.updateReview(props.existingReview.id, {
        rating: form.value.rating,
        title: form.value.title || null,
        comment: form.value.comment || null,
      });
      toastStore.success('Review updated successfully! It will be reviewed before being published.');
    } else {
      // Create new review
      review = await ReviewService.createReview({
        product_id: props.productId,
        user_id: authStore.user.id,
        rating: form.value.rating,
        title: form.value.title || null,
        comment: form.value.comment || null,
      });
      toastStore.success('Review submitted successfully! It will be reviewed before being published.');
    }

    emit('success', review);
  } catch (error) {
    console.error('Error submitting review:', error);
    toastStore.error('Failed to submit review. Please try again.');
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  // Pre-fill form if editing existing review
  if (props.existingReview) {
    form.value = {
      rating: props.existingReview.rating,
      title: props.existingReview.title || '',
      comment: props.existingReview.comment || '',
    };
  }
});
</script>

<style scoped>
.btn-primary {
  @apply bg-primary-600 text-white hover:bg-primary-700;
}
</style>
