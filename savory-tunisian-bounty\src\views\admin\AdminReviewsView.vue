<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Review Management</h1>
        <p class="text-gray-600">Moderate customer reviews and manage featured content</p>
      </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="p-2 bg-yellow-100 rounded-lg">
            <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Pending Reviews</p>
            <p class="text-2xl font-semibold text-gray-900">{{ pendingReviews.length }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="p-2 bg-green-100 rounded-lg">
            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Approved Reviews</p>
            <p class="text-2xl font-semibold text-gray-900">{{ approvedReviews.length }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="p-2 bg-blue-100 rounded-lg">
            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Featured Reviews</p>
            <p class="text-2xl font-semibold text-gray-900">{{ featuredReviews.length }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="p-2 bg-purple-100 rounded-lg">
            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-1l-4 4z" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Total Reviews</p>
            <p class="text-2xl font-semibold text-gray-900">{{ allReviews.length }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-white rounded-lg shadow p-6">
      <div class="flex flex-wrap gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
          <select
            v-model="selectedStatus"
            @change="loadReviews"
            class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <option value="">All Reviews</option>
            <option value="pending">Pending Approval</option>
            <option value="approved">Approved</option>
            <option value="featured">Featured</option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Rating</label>
          <select
            v-model="selectedRating"
            @change="loadReviews"
            class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <option value="">All Ratings</option>
            <option value="5">5 Stars</option>
            <option value="4">4 Stars</option>
            <option value="3">3 Stars</option>
            <option value="2">2 Stars</option>
            <option value="1">1 Star</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Reviews List -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-lg font-medium text-gray-900">
          {{ getFilteredReviews().length }} Reviews
        </h2>
      </div>

      <div v-if="loading" class="p-8 text-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
        <p class="text-gray-500 mt-2">Loading reviews...</p>
      </div>

      <div v-else-if="getFilteredReviews().length === 0" class="p-8 text-center">
        <p class="text-gray-500">No reviews found matching your criteria.</p>
      </div>

      <div v-else class="divide-y divide-gray-200">
        <div
          v-for="review in getFilteredReviews()"
          :key="review.id"
          class="p-6 hover:bg-gray-50"
        >
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <!-- Review Header -->
              <div class="flex items-center space-x-4 mb-3">
                <div class="flex items-center space-x-2">
                  <span class="font-medium text-gray-900">
                    {{ review.profiles?.full_name || 'Anonymous' }}
                  </span>
                  <span class="text-sm text-gray-500">
                    ({{ review.profiles?.email }})
                  </span>
                </div>
                <div class="flex">
                  <span
                    v-for="star in 5"
                    :key="star"
                    class="text-sm"
                    :class="star <= review.rating ? 'text-yellow-400' : 'text-gray-300'"
                  >
                    ★
                  </span>
                </div>
                <span class="text-sm text-gray-500">
                  {{ formatDate(review.created_at) }}
                </span>
              </div>

              <!-- Product Info -->
              <div class="mb-3">
                <span class="text-sm text-gray-600">Product: </span>
                <span class="text-sm font-medium text-gray-900">
                  {{ review.products?.name }}
                </span>
              </div>

              <!-- Review Content -->
              <div class="mb-4">
                <h4 v-if="review.title" class="font-medium text-gray-900 mb-2">
                  {{ review.title }}
                </h4>
                <p v-if="review.comment" class="text-gray-700">
                  {{ review.comment }}
                </p>
              </div>

              <!-- Status Badges -->
              <div class="flex items-center space-x-2">
                <span
                  :class="[
                    'px-2 py-1 text-xs rounded-full',
                    review.is_approved
                      ? 'bg-green-100 text-green-800'
                      : 'bg-yellow-100 text-yellow-800'
                  ]"
                >
                  {{ review.is_approved ? 'Approved' : 'Pending' }}
                </span>
                <span
                  v-if="review.is_featured"
                  class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800"
                >
                  Featured
                </span>
              </div>
            </div>

            <!-- Actions -->
            <div class="flex items-center space-x-2 ml-4">
              <button
                v-if="!review.is_approved"
                @click="approveReview(review)"
                class="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700 transition-colors"
              >
                Approve
              </button>
              <button
                @click="toggleFeature(review)"
                :class="[
                  'px-3 py-1 text-sm rounded transition-colors',
                  review.is_featured
                    ? 'bg-gray-600 text-white hover:bg-gray-700'
                    : 'bg-blue-600 text-white hover:bg-blue-700'
                ]"
              >
                {{ review.is_featured ? 'Unfeature' : 'Feature' }}
              </button>
              <button
                @click="deleteReview(review)"
                class="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 transition-colors"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { ReviewService } from '@/services/reviewService';
import { useToastStore } from '@/stores/toast';
import type { Review } from '@/types';

const toastStore = useToastStore();

const loading = ref(false);
const allReviews = ref<Review[]>([]);
const selectedStatus = ref('');
const selectedRating = ref('');

const pendingReviews = computed(() => 
  allReviews.value.filter(review => !review.is_approved)
);

const approvedReviews = computed(() => 
  allReviews.value.filter(review => review.is_approved)
);

const featuredReviews = computed(() => 
  allReviews.value.filter(review => review.is_featured)
);

const getFilteredReviews = () => {
  let filtered = [...allReviews.value];

  if (selectedStatus.value === 'pending') {
    filtered = filtered.filter(review => !review.is_approved);
  } else if (selectedStatus.value === 'approved') {
    filtered = filtered.filter(review => review.is_approved);
  } else if (selectedStatus.value === 'featured') {
    filtered = filtered.filter(review => review.is_featured);
  }

  if (selectedRating.value) {
    filtered = filtered.filter(review => review.rating === parseInt(selectedRating.value));
  }

  return filtered;
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

const loadReviews = async () => {
  loading.value = true;
  try {
    allReviews.value = await ReviewService.getAllReviews();
  } catch (error) {
    console.error('Error loading reviews:', error);
    toastStore.error('Failed to load reviews');
  } finally {
    loading.value = false;
  }
};

const approveReview = async (review: Review) => {
  try {
    await ReviewService.approveReview(review.id);
    review.is_approved = true;
    toastStore.success('Review approved successfully');
  } catch (error) {
    console.error('Error approving review:', error);
    toastStore.error('Failed to approve review');
  }
};

const toggleFeature = async (review: Review) => {
  try {
    const newFeaturedStatus = !review.is_featured;
    await ReviewService.featureReview(review.id, newFeaturedStatus);
    review.is_featured = newFeaturedStatus;
    toastStore.success(
      newFeaturedStatus ? 'Review featured successfully' : 'Review unfeatured successfully'
    );
  } catch (error) {
    console.error('Error toggling feature status:', error);
    toastStore.error('Failed to update feature status');
  }
};

const deleteReview = async (review: Review) => {
  if (!confirm('Are you sure you want to delete this review? This action cannot be undone.')) {
    return;
  }

  try {
    await ReviewService.deleteReview(review.id);
    allReviews.value = allReviews.value.filter(r => r.id !== review.id);
    toastStore.success('Review deleted successfully');
  } catch (error) {
    console.error('Error deleting review:', error);
    toastStore.error('Failed to delete review');
  }
};

onMounted(() => {
  loadReviews();
});
</script>
