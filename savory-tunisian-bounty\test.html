<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Slider Optimisé</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        .slider-container {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            position: relative;
            overflow: hidden;
            touch-action: pan-y;
            transform: translateZ(0); /* Enable hardware acceleration */
        }

        .slider {
            display: flex;
            transition: transform 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            will-change: transform;
        }

        .slide {
            flex: 0 0 100%;
            position: relative;
            height: 500px;
            background-size: cover;
            background-position: center;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            padding: 20px;
            transform: translateZ(0); /* Enable hardware acceleration */
        }

        .slide-content {
            max-width: 800px;
            margin: 0 auto;
            transform: translateZ(0); /* Enable hardware acceleration */
        }

        .slide-heading {
            font-family: 'Poppins', sans-serif;
            font-size: 36px;
            font-weight: 600;
            text-transform: uppercase;
            margin-bottom: 20px;
            text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
        }

        .slide-subtitle {
            font-size: 20px;
            margin-bottom: 30px;
            color: #000;
            text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
        }

        .slide-button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #D60000;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-weight: bold;
            transition: all 0.3s ease;
            transform: translateZ(0); /* Enable hardware acceleration */
        }

        .slide-button:hover {
            background-color: #B20000;
            transform: translateY(-2px) translateZ(0);
        }

        .slider-controls {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 10px;
            z-index: 10;
        }

        .slider-control {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.5);
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid #D60000;
        }

        .slider-control.active {
            background-color: #D60000;
            transform: scale(1.2);
        }

        .loading {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.3);
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            font-size: 24px;
        }

        @media (max-width: 768px) {
            .slide-heading {
                font-size: 28px;
            }

            .slide-subtitle {
                font-size: 16px;
            }

            .slide-button {
                padding: 10px 20px;
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="slider-container">
        <div class="slider">
            <div class="slide" data-index="0">
                <div class="loading">Chargement...</div>
                <div class="slide-content">
                    <div class="slide-heading">Qui sommes-nous ?</div>
                    <a href="#" class="slide-button">Lire plus</a>
                </div>
            </div>
            <div class="slide" data-index="1">
                <div class="loading">Chargement...</div>
                <div class="slide-content">
                    <div class="slide-heading">Nous assurons la livraison partout dans le monde</div>
                    <div class="slide-subtitle">LIVRAISON GRATUITE À PARTIR DE 50 €</div>
                    <a href="#" class="slide-button">En savoir plus</a>
                </div>
            </div>
            <div class="slide" data-index="2">
                <div class="loading">Chargement...</div>
                <div class="slide-content">
                    <div class="slide-heading">Nos produits écologiques</div>
                    <div class="slide-subtitle">Découvrez notre gamme respectueuse de l'environnement</div>
                    <a href="#" class="slide-button">Voir la collection</a>
                </div>
            </div>
        </div>
        <div class="slider-controls">
            <div class="slider-control active" data-index="0"></div>
            <div class="slider-control" data-index="1"></div>
            <div class="slider-control" data-index="2"></div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // DOM Elements
            const slider = document.querySelector('.slider');
            const sliderContainer = document.querySelector('.slider-container');
            const slides = document.querySelectorAll('.slide');
            const controls = document.querySelectorAll('.slider-control');

            // State variables
            let currentSlide = 0;
            let isDragging = false;
            let startPos = 0;
            let currentTranslate = 0;
            let prevTranslate = 0;
            let animationID = 0;
            let autoSlideInterval;
            let isTransitioning = false;
            const slideCount = slides.length;
            
            // Clone first and last slides for infinite loop
            const firstSlideClone = slides[0].cloneNode(true);
            const lastSlideClone = slides[slideCount - 1].cloneNode(true);
            
            // Add clones to the slider
            slider.appendChild(firstSlideClone);
            slider.insertBefore(lastSlideClone, slides[0]);
            
            // Adjust initial position to show first real slide (not clone)
            currentSlide = 1;
            
            // Initialize slider
            initSlider();
            setupEventListeners();
            startAutoSlide();

            /**
             * Initialize slider with background images and loading states
             */
            function initSlider() {
                // Set background images and handle loading
                document.querySelectorAll('.slide').forEach((slide, index) => {
                    const img = new Image();
                    // Adjust index to account for cloned slides
                    const realIndex = (index === 0) ? slideCount - 1 : 
                                     (index === slideCount + 1) ? 0 : index - 1;
                    
                    img.src = `https://picsum.photos/1200/500?random=${realIndex + 1}`;

                    // Set background image when loaded
                    img.onload = () => {
                        slide.style.backgroundImage = `url('${img.src}')`;
                        removeLoadingIndicator(slide);
                    };

                    // Handle image load errors
                    img.onerror = () => {
                        console.warn(`Failed to load image for slide ${realIndex}`);
                        slide.style.backgroundImage = "url('https://picsum.photos/1200/500?random=fallback')";
                        removeLoadingIndicator(slide);
                    };

                    // Set timeout for loading indicator
                    setTimeout(() => {
                        if (slide.querySelector('.loading')) {
                            slide.style.backgroundImage = "url('https://picsum.photos/1200/500?random=fallback')";
                            removeLoadingIndicator(slide);
                        }
                    }, 5000);
                });

                // Initial position update using requestAnimationFrame for smoother rendering
                requestAnimationFrame(() => {
                    updateSliderPosition(false);
                });
            }

            /**
             * Remove loading indicator from slide
             * @param {HTMLElement} slide - The slide element
             */
            function removeLoadingIndicator(slide) {
                const loading = slide.querySelector('.loading');
                if (loading) {
                    loading.style.opacity = '0';
                    setTimeout(() => {
                        loading.remove();
                    }, 300);
                }
            }

            /**
             * Set up all event listeners
             */
            function setupEventListeners() {
                // Dot navigation
                controls.forEach(control => {
                    control.addEventListener('click', handleControlClick);
                });

                // Keyboard navigation
                document.addEventListener('keydown', handleKeyDown);

                // Mouse and touch events with passive: false for touch events to prevent scrolling
                slider.addEventListener('mousedown', startDrag);
                slider.addEventListener('touchstart', startDrag, { passive: false });
                
                // Use window for mouse/touch move and end to capture events outside slider
                window.addEventListener('mouseup', endDrag);
                window.addEventListener('touchend', endDrag);
                window.addEventListener('mousemove', drag);
                window.addEventListener('touchmove', drag, { passive: false });
                slider.addEventListener('mouseleave', endDrag);

                // Critical for infinite loop: detect when transitions end to handle slide jumps
                slider.addEventListener('transitionend', handleTransitionEnd);

                // Pause auto-slide on hover
                sliderContainer.addEventListener('mouseenter', pauseAutoSlide);
                sliderContainer.addEventListener('mouseleave', startAutoSlide);
                
                // Debounced resize handler to prevent performance issues
                let resizeTimer;
                window.addEventListener('resize', () => {
                    clearTimeout(resizeTimer);
                    resizeTimer = setTimeout(() => {
                        // Recalculate slider position on resize
                        updateSliderPosition(false);
                    }, 250);
                });
            }

            /**
             * Handle control dot click
             * @param {Event} e - The click event
             */
            function handleControlClick(e) {
                if (isTransitioning) return;
                
                const index = parseInt(e.currentTarget.getAttribute('data-index'));
                // Add 1 to account for cloned slide at beginning
                goToSlide(index + 1);
            }

            /**
             * Handle keyboard navigation
             * @param {Event} e - The keydown event
             */
            function handleKeyDown(e) {
                if (isTransitioning) return; // Prevent rapid transitions
                
                if (e.key === 'ArrowRight') {
                    nextSlide();
                } else if (e.key === 'ArrowLeft') {
                    prevSlide();
                }
            }

            /**
             * Start dragging the slider
             * @param {Event} e - The mousedown/touchstart event
             */
            function startDrag(e) {
                if (isTransitioning) return;

                // Always prevent default for touch events to avoid scrolling
                if (e.type === 'touchstart') {
                    e.preventDefault();
                    startPos = e.touches[0].clientX;
                } else {
                    startPos = e.clientX;
                }

                // Cancel any ongoing animation
                cancelAnimationFrame(animationID);
                
                isDragging = true;
                slider.style.transition = 'none';
                prevTranslate = currentTranslate = getCurrentTranslate();
                sliderContainer.style.cursor = 'grabbing';
            }

            /**
             * Get current translate value from computed style
             * @returns {number} The current translate X value
             */
            function getCurrentTranslate() {
                const transform = window.getComputedStyle(slider).transform;
                if (transform === 'none') return 0;
                const matrix = transform.match(/^matrix\((.+)\)$/);
                if (matrix) {
                    return parseFloat(matrix[1].split(', ')[4]);
                }
                return 0;
            }

            /**
             * Handle dragging with requestAnimationFrame for smooth animation
             * @param {Event} e - The mousemove/touchmove event
             */
            function drag(e) {
                if (!isDragging) return;
                
                // Always prevent default for touch events to prevent page scrolling
                if (e.type === 'touchmove') {
                    e.preventDefault();
                }

                const currentPosition = e.type === 'touchmove' ? e.touches[0].clientX : e.clientX;
                const moveDistance = currentPosition - startPos;
                
                // Calculate drag distance as percentage of container width
                const containerWidth = sliderContainer.offsetWidth;
                const dragPercentage = moveDistance / containerWidth;
                
                // Apply progressive resistance at edges (more resistance as you pull further)
                const totalSlides = document.querySelectorAll('.slide').length;
                const isAtStart = currentSlide === 0;
                const isAtEnd = currentSlide === totalSlides - 1;
                
                let resistance = 1;
                if ((isAtStart && moveDistance > 0) || (isAtEnd && moveDistance < 0)) {
                    // Calculate resistance factor (0.1 to 0.9) based on drag distance
                    const maxResistance = 0.9;
                    const resistanceFactor = Math.min(Math.abs(dragPercentage) * 2, maxResistance);
                    resistance = 1 - resistanceFactor;
                }
                
                currentTranslate = prevTranslate + (moveDistance * resistance);
                
                // Use requestAnimationFrame for smooth animation
                cancelAnimationFrame(animationID);
                animationID = requestAnimationFrame(() => {
                    slider.style.transform = `translateX(${currentTranslate}px)`;
                });
            }

            /**
             * End dragging
             */
            function endDrag() {
                if (!isDragging) return;

                isDragging = false;
                sliderContainer.style.cursor = '';
                
                // Cancel any ongoing animation
                cancelAnimationFrame(animationID);
                
                // Enhanced transition with cubic-bezier for more natural movement
                slider.style.transition = 'transform 0.7s cubic-bezier(0.25, 0.1, 0.25, 1.0)';

                const movedBy = currentTranslate - prevTranslate;
                const containerWidth = sliderContainer.offsetWidth;
                const totalSlides = document.querySelectorAll('.slide').length;

                // If moved enough to change slide (20% threshold)
                if (movedBy < -containerWidth * 0.2) {
                    currentSlide = Math.min(currentSlide + 1, totalSlides - 1);
                    isTransitioning = true;
                } else if (movedBy > containerWidth * 0.2) {
                    currentSlide = Math.max(currentSlide - 1, 0);
                    isTransitioning = true;
                }

                updateSliderPosition(true);
                startAutoSlide();
            }

            /**
             * Handle transition end for infinite loop
             * This is critical for the infinite loop functionality
             */
            function handleTransitionEnd() {
                if (!isTransitioning) return;

                const totalSlides = document.querySelectorAll('.slide').length;

                const reEnableTransition = () => {
                    // Add a short delay to ensure browser completes rendering
                    setTimeout(() => {
                        slider.style.transition = 'transform 0.7s cubic-bezier(0.25, 0.1, 0.25, 1.0)';
                        isTransitioning = false;
                    }, 60); // 50–100ms delay range; tuned to 60ms
                };

                if (currentSlide === 0) {
                    // Jump from cloned last to real last
                    slider.style.transition = 'none';
                    currentSlide = slideCount;

                    requestAnimationFrame(() => {
                        updateSliderPosition(false);
                        requestAnimationFrame(() => {
                            reEnableTransition();
                        });
                    });

                } else if (currentSlide === totalSlides - 1) {
                    // Jump from cloned first to real first
                    slider.style.transition = 'none';
                    currentSlide = 1;

                    requestAnimationFrame(() => {
                        updateSliderPosition(false);
                        requestAnimationFrame(() => {
                            reEnableTransition();
                        });
                    });

                } else {
                    isTransitioning = false;
                }
            }

            /**
             * Update slider position based on current slide
             * @param {boolean} animate - Whether to animate the transition
             */
            function updateSliderPosition(animate) {
                if (!animate) {
                    slider.style.transition = 'none';
                } else {
                    slider.style.transition = 'transform 0.7s cubic-bezier(0.25, 0.1, 0.25, 1.0)';
                }
                
                // Read layout once to avoid thrashing
                const containerWidth = sliderContainer.offsetWidth;
                currentTranslate = -currentSlide * containerWidth;
                
                // Batch write operations with requestAnimationFrame
                requestAnimationFrame(() => {
                    // Apply hardware acceleration
                    slider.style.transform = `translateX(${currentTranslate}px) translateZ(0)`;
                    updateControls();
                    
                    if (!animate) {
                        // Re-enable transition after position update
                        requestAnimationFrame(() => {
                            slider.style.transition = 'transform 0.7s cubic-bezier(0.25, 0.1, 0.25, 1.0)';
                        });
                    }
                });
            }

            /**
             * Update control dots to reflect current slide (accounting for clones)
             */
            function updateControls() {
                // Adjust for cloned slides: real slides are at positions 1 to slideCount
                const realIndex = (currentSlide === 0) ? slideCount - 1 : 
                                 (currentSlide > slideCount) ? 0 : currentSlide - 1;
                
                controls.forEach((control, index) => {
                    control.classList.toggle('active', index === realIndex);
                });
            }

            /**
             * Go to specific slide
             * @param {number} index - The slide index to go to
             */
            function goToSlide(index) {
                if (index === currentSlide || isTransitioning) return;

                currentSlide = index;
                isTransitioning = true;
                updateSliderPosition(true);
                resetAutoSlide();
            }

            /**
             * Go to next slide with infinite loop
             */
            function nextSlide() {
                if (isTransitioning) return;

                isTransitioning = true;
                currentSlide++;
                updateSliderPosition(true);
                resetAutoSlide();
            }

            /**
             * Go to previous slide with infinite loop
             */
            function prevSlide() {
                if (isTransitioning) return;

                isTransitioning = true;
                currentSlide--;
                updateSliderPosition(true);
                resetAutoSlide();
            }

            /**
             * Start auto-slide interval
             */
            function startAutoSlide() {
                clearInterval(autoSlideInterval);
                autoSlideInterval = setInterval(() => {
                    if (!isTransitioning && !isDragging) {
                        nextSlide();
                    }
                }, 5000);
            }

            /**
             * Pause auto-slide
             */
            function pauseAutoSlide() {
                clearInterval(autoSlideInterval);
            }

            /**
             * Reset auto-slide interval
             */
            function resetAutoSlide() {
                pauseAutoSlide();
                startAutoSlide();
            }
        });
    </script>
</body>
</html>
