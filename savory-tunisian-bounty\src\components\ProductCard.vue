<template>
  <div
    class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg transition-all duration-300 group"
  >
    <div class="relative overflow-hidden">
      <img
        :src="product.image_url || '/placeholder-product.jpg'"
        :alt="product.name"
        class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
        @error="handleImageError"
      />

      <!-- Overlay badges -->
      <div class="absolute top-3 left-3 flex flex-col space-y-2">
        <span
          v-if="product.stock_quantity <= 5"
          class="bg-red-500 text-white text-xs px-2 py-1 rounded-full font-medium"
        >
          Low Stock
        </span>
        <span
          v-if="product.is_organic"
          class="bg-green-500 text-white text-xs px-2 py-1 rounded-full font-medium"
        >
          Organic
        </span>
      </div>

      <!-- Quick action overlay -->
      <div
        class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center"
      >
        <button
          @click="viewProduct"
          class="opacity-0 group-hover:opacity-100 transform translate-y-2 group-hover:translate-y-0 transition-all duration-300 bg-white text-gray-900 px-4 py-2 rounded-lg font-medium shadow-lg"
        >
          Quick View
        </button>
      </div>
    </div>

    <div class="p-6">
      <!-- Category badge -->
      <div class="mb-3">
        <span
          v-if="product.category"
          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800"
        >
          {{ getCategoryName(product.category_id) }}
        </span>
      </div>

      <h3
        class="text-lg font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-primary-600 transition-colors"
      >
        {{ product.name }}
      </h3>

      <p class="text-gray-600 text-sm mb-4 line-clamp-2">
        {{ product.description }}
      </p>

      <!-- Product features -->
      <div class="flex flex-wrap gap-1 mb-4">
        <span
          v-if="product.is_vegan"
          class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800"
        >
          Vegan
        </span>
        <span
          v-if="product.is_gluten_free"
          class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800"
        >
          Gluten Free
        </span>
        <span
          v-if="product.is_fair_trade"
          class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800"
        >
          Fair Trade
        </span>
      </div>

      <!-- Price and stock -->
      <div class="flex items-center justify-between mb-4">
        <div>
          <span class="text-2xl font-bold text-primary-600">
            ${{ product.price.toFixed(2) }}
          </span>
          <div class="text-xs text-gray-500 mt-1">
            {{ product.stock_quantity }} in stock
          </div>
        </div>

        <div v-if="isInCart" class="text-right">
          <div class="text-sm font-medium text-primary-600">In Cart</div>
          <div class="text-xs text-gray-500">Qty: {{ cartQuantity }}</div>
        </div>
      </div>

      <!-- Action buttons -->
      <div class="flex space-x-2">
        <button @click="viewProduct" class="flex-1 btn-outline text-sm py-2.5">
          Details
        </button>

        <button
          @click="addToCart"
          :disabled="product.stock_quantity === 0"
          class="flex-1 btn-primary text-sm py-2.5 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span v-if="isInCart">Update</span>
          <span v-else>Add to Cart</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useRouter } from "vue-router";
import { useCartStore } from "@/stores/cart";
import { useProductsStore } from "@/stores/products";
import type { Product } from "@/types";

interface Props {
  product: Product;
}

const props = defineProps<Props>();
const router = useRouter();
const cartStore = useCartStore();
const productsStore = useProductsStore();

const isInCart = computed(() => cartStore.isInCart(props.product.id));
const cartQuantity = computed(() =>
  cartStore.getItemQuantity(props.product.id)
);

const getCategoryName = (categoryId: string) => {
  const category = productsStore.categories.find((c) => c.id === categoryId);
  return category?.name || "";
};

const viewProduct = () => {
  router.push({ name: "product-detail", params: { id: props.product.id } });
};

const addToCart = () => {
  if (props.product.stock_quantity > 0) {
    cartStore.addItem(props.product, 1);
  }
};

const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement;
  target.src =
    "https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=400";
};
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
