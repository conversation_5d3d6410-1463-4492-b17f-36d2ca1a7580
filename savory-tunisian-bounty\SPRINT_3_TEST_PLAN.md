# Sprint 3: Shopping Cart Core Functionality - Test Plan

## ✅ User Stories Completed

### US012: Add products to shopping cart ✅
**Test Steps:**
1. Navigate to Products page
2. Click "Add to Cart" on any product
3. Verify toast notification appears: "Added [Product Name] to cart"
4. Verify cart badge in header updates with item count
5. Click "Add to Cart" again on same product
6. Verify toast notification: "Updated [Product Name] quantity in cart"

### US013: View cart contents ✅
**Test Steps:**
1. Add items to cart (follow US012 steps)
2. Click cart icon in header
3. Verify cart page displays:
   - Product images, names, and prices
   - Individual item quantities
   - Item subtotals
   - Total cart amount
   - Order summary section

### US014: Update item quantities ✅
**Test Steps:**
1. Add items to cart and navigate to cart page
2. Use +/- buttons to change quantities
3. Verify:
   - Quantity updates immediately
   - Item subtotal recalculates
   - Total cart amount updates
   - Cart badge in header updates
   - Cannot decrease below 1
   - Cannot increase beyond stock quantity

### US015: Remove items from cart ✅
**Test Steps:**
1. Add items to cart and navigate to cart page
2. Click "Remove" button on any item
3. Verify:
   - Toast notification: "Removed [Product Name] from cart"
   - Item disappears from cart
   - Cart totals update
   - Cart badge updates
4. Remove all items
5. Verify empty cart state displays with "Start Shopping" button

## 🎯 Additional Features Implemented

### Cart Persistence ✅
**Test Steps:**
1. Add items to cart
2. Refresh the page
3. Verify cart contents are preserved
4. Close browser and reopen
5. Verify cart contents still preserved

### Visual Feedback ✅
**Test Steps:**
1. Add item to cart - verify success toast
2. Update quantity - verify cart updates
3. Remove item - verify info toast
4. Clear cart - verify info toast with item count

### Stock Validation ✅
**Test Steps:**
1. Find product with limited stock
2. Try to add more than available stock
3. Verify quantity is limited to stock amount
4. Verify "Add to Cart" button disabled when out of stock

### Cart Navigation ✅
**Test Steps:**
1. From cart page, click "Continue Shopping"
2. Verify navigates to products page
3. From cart page, click "Proceed to Checkout"
4. Verify navigates to checkout (when implemented)

## 🧪 Edge Cases to Test

### Empty Cart Handling ✅
1. Navigate to cart when empty
2. Verify empty state message and shopping button

### Large Quantities ✅
1. Try adding large quantities
2. Verify system handles appropriately

### Multiple Products ✅
1. Add multiple different products
2. Verify all display correctly
3. Test mixed operations (add, remove, update)

### Browser Storage ✅
1. Test with browser storage disabled
2. Verify graceful degradation

## 🎉 Sprint 3 Status: COMPLETE

All user stories have been successfully implemented with additional enhancements:

- ✅ US012: Add products to shopping cart
- ✅ US013: View cart contents  
- ✅ US014: Update item quantities
- ✅ US015: Remove items from cart

**Bonus Features Added:**
- Toast notification system for better UX
- Cart persistence with localStorage
- Stock quantity validation
- Responsive design
- Empty cart state
- Cart badge with item count
- Smooth animations and transitions

**Total Story Points Completed:** 11/11 ✅

Ready to proceed to Sprint 4: Checkout & Order Placement!
